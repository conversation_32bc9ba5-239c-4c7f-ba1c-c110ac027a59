#!/usr/bin/env python3
"""
Test script for Backspace evaluations
Run this to validate your autonomous coding agent implementation
"""

import asyncio
import sys
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.evaluation.backspace_test_runner import (
    run_backspace_integration_test,
    run_quick_backspace_eval,
    compare_with_baseline
)


async def test_evaluation_system():
    """Test the Backspace evaluation system"""
    
    print("🚀 BACKSPACE AUTONOMOUS CODING AGENT EVALUATION")
    print("=" * 60)
    print()
    print("This test validates your implementation against the Backspace specification:")
    print("✅ Streaming API with Server-Sent Events")
    print("✅ Sandboxed code execution") 
    print("✅ Autonomous PR creation")
    print("✅ Real-time observability")
    print()
    
    # Step 1: Check if API server is running
    print("🔧 STEP 1: Integration Test")
    print("-" * 30)
    
    try:
        integration_result = await run_backspace_integration_test()
        
        if integration_result["integration_success"]:
            print("✅ Integration test PASSED - API is working correctly")
        else:
            print("❌ Integration test FAILED - Check your API server")
            print("💡 Make sure to run: python -m src.api.main")
            print("💡 Server should be accessible at http://localhost:8000")
            return False
            
    except Exception as e:
        print(f"❌ Integration test ERROR: {e}")
        print("💡 Make sure your API server is running on http://localhost:8000")
        return False
    
    print()
    
    # Step 2: Quick functional test
    print("⚡ STEP 2: Quick Functional Test")
    print("-" * 30)
    
    try:
        quick_result = await run_quick_backspace_eval()
        
        success_rate = quick_result["success_rate"]
        avg_score = quick_result["average_score"]
        
        print(f"📊 Success Rate: {success_rate:.1%}")
        print(f"📈 Average Score: {avg_score:.2f}")
        
        if success_rate >= 0.5:  # At least 50% success
            print("✅ Quick test PASSED - Basic functionality working")
        else:
            print("⚠️  Quick test shows issues - Check implementation")
            
    except Exception as e:
        print(f"❌ Quick test ERROR: {e}")
        return False
    
    print()
    
    # Step 3: Baseline comparison
    print("📊 STEP 3: Baseline Comparison")
    print("-" * 30)
    
    try:
        baseline_result = await compare_with_baseline()
        
        if baseline_result.get("performance_regression", False):
            print("⚠️  Performance regression detected")
            for rec in baseline_result.get("recommendations", []):
                print(f"  💡 {rec}")
        else:
            print("✅ Performance within expected baseline")
            
    except Exception as e:
        print(f"❌ Baseline comparison ERROR: {e}")
    
    print()
    
    # Final assessment
    print("🎯 FINAL ASSESSMENT")
    print("-" * 30)
    
    if integration_result["integration_success"] and quick_result["success_rate"] >= 0.3:
        print("🎉 CONGRATULATIONS! Your Backspace implementation is working!")
        print()
        print("Next steps:")
        print("1. 📧 <NAME_EMAIL>")
        print("2. 🎥 Record demo video (optional)")
        print("3. 📋 Include README with setup instructions")
        print()
        print("Your implementation includes:")
        print(f"  ✅ Streaming API: {integration_result['api_metrics']['streaming_works']}")
        print(f"  ✅ SSE Format: {integration_result['api_metrics']['sse_format_correct']}")
        print(f"  ✅ PR Creation: {integration_result['api_metrics']['pr_url_returned']}")
        print(f"  📊 Success Rate: {quick_result['success_rate']:.1%}")
        print(f"  📈 Quality Score: {quick_result['average_score']:.2f}")
        
        return True
    else:
        print("⚠️  Implementation needs work:")
        if not integration_result["integration_success"]:
            print("  ❌ API integration issues")
        if quick_result["success_rate"] < 0.3:
            print("  ❌ Low success rate on functional tests")
        
        print()
        print("Debug steps:")
        print("1. Check API server logs")
        print("2. Verify environment variables (ANTHROPIC_API_KEY, GITHUB_TOKEN)")
        print("3. Test sandbox functionality")
        print("4. Validate streaming events format")
        
        return False


async def run_comprehensive_test():
    """Run comprehensive evaluation for final submission"""
    
    print("🔥 COMPREHENSIVE BACKSPACE EVALUATION")
    print("=" * 60)
    print("Running full vibe evaluation suite (SWE-bench methodology)")
    print()
    
    from src.evaluation.backspace_eval_suite import run_backspace_vibe_evaluation
    
    try:
        vibe_result = await run_backspace_vibe_evaluation()
        
        print()
        print("📊 COMPREHENSIVE RESULTS")
        print("-" * 30)
        print(f"Tasks Completed: {vibe_result['total_tasks']}")
        print(f"Success Rate: {vibe_result['success_rate']:.1%}")
        print(f"Average Vibe Score: {vibe_result['average_vibe_score']:.2f}")
        print(f"Focus Areas: {vibe_result['inconsistent_tasks']}")
        
        # Save results
        results_file = Path("backspace_comprehensive_results.json")
        with open(results_file, 'w') as f:
            json.dump(vibe_result, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {results_file}")
        
        if vibe_result['success_rate'] >= 0.6:
            print("\n🎉 EXCELLENT! Your implementation is submission-ready!")
        elif vibe_result['success_rate'] >= 0.4:
            print("\n✅ GOOD! Implementation is functional with room for improvement")
        else:
            print("\n⚠️  Implementation needs significant work")
        
        return vibe_result
        
    except Exception as e:
        print(f"❌ Comprehensive evaluation ERROR: {e}")
        return None


def main():
    """Main test runner"""
    
    if len(sys.argv) > 1 and sys.argv[1] == "--comprehensive":
        # Run comprehensive evaluation
        result = asyncio.run(run_comprehensive_test())
        exit_code = 0 if result and result['success_rate'] >= 0.4 else 1
    else:
        # Run basic validation
        success = asyncio.run(test_evaluation_system())
        exit_code = 0 if success else 1
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()