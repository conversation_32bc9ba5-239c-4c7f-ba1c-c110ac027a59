# Backspace Autonomous Coding Agent Evaluation Guide

This guide explains how to evaluate your autonomous coding agent implementation using the comprehensive evaluation framework based on SWE-bench winning team insights.

## Quick Start

### 1. Basic Validation Test

First, validate that your implementation works:

```bash
# Run the quick validation test
python test_backspace_evals.py

# This tests:
# ✅ API server accessibility
# ✅ Streaming SSE functionality  
# ✅ Basic agent workflow
# ✅ PR creation capability
```

### 2. Comprehensive Evaluation

For thorough evaluation using vibe eval methodology:

```bash
# Run comprehensive vibe evaluation
python test_backspace_evals.py --comprehensive

# This runs 5-10 carefully chosen tasks following SWE-bench methodology
```

### 3. Integration with Existing Framework

Use the existing eval runner with Backspace-specific suites:

```bash
# List all available evaluation suites
python -m src.evaluation.eval_runner list

# Run Backspace vibe evaluation
python -m src.evaluation.eval_runner eval backspace-vibe

# Run consistency analysis (find 50/50 success rate scenarios)
python -m src.evaluation.eval_runner eval backspace-consistency
```

## Evaluation Methodology

### SWE-bench Insights Applied

This evaluation system implements key insights from the SWE-bench winning team:

1. **Start with Vibe Evals** (5-10 examples)
   - Build intuition about system performance
   - Focus on end-to-end workflow validation
   - Identify patterns in success/failure

2. **End-to-End Evaluation First**
   - Don't optimize components in isolation
   - Agent persistence compensates for tool weaknesses
   - Validate full workflow before drilling down

3. **Focus on Inconsistent Performance**
   - 50/50 success rate scenarios reveal most about limitations
   - Fast vs slow patterns on same task type
   - These edge cases drive improvement priorities

### Backspace-Specific Evaluations

#### Vibe Evaluation Suite (`backspace-vibe`)

8 carefully chosen tasks covering:

- **Easy**: Health check endpoint, basic feature addition
- **Medium**: Input validation, user authentication  
- **Hard**: Bug fixes, complex refactoring
- **Edge Cases**: Minimal repos, ambiguous requirements
- **Consistency Tests**: Same prompt on different repos

**Example:**
```bash
python -m src.evaluation.eval_runner eval backspace-vibe --output vibe_results.json
```

#### Consistency Analysis Suite (`backspace-consistency`)

Designed to find 50/50 success rate scenarios:

- Same prompt on different repositories
- Boundary conditions (large codebases)
- Ambiguous requirements
- Performance edge cases

## Evaluation Metrics

### Core Backspace Metrics

1. **API Functionality** (40% weight)
   - ✅ Server accessibility
   - ✅ Streaming SSE format compliance
   - ✅ Required event types present
   - ✅ Workflow completion

2. **PR Creation & Quality** (40% weight)
   - ✅ PR successfully created
   - ✅ Appropriate branch naming
   - ✅ Meaningful title and description
   - ✅ Relevant files changed

3. **Performance & Reliability** (20% weight)
   - ⏱️ Response time < 2 minutes preferred
   - 📊 Sufficient agent activity (>5 events)
   - 🔄 Consistency across runs

### Advanced Metrics

- **Vibe Score**: Holistic assessment of implementation quality (0.0-1.0)
- **Performance Pattern**: Classification of execution behavior
- **Consistency Rating**: Reliability across similar tasks
- **Focus Areas**: Tasks requiring attention (50/50 success rate)

## Running Evaluations

### Method 1: Simple Test Script

```bash
# Quick validation (recommended for development)
python test_backspace_evals.py

# Comprehensive evaluation (for final assessment)
python test_backspace_evals.py --comprehensive
```

### Method 2: Integrated Eval Runner

```bash
# Backspace vibe evaluation (SWE-bench methodology)
python -m src.evaluation.eval_runner eval backspace-vibe

# Consistency analysis (find edge cases)
python -m src.evaluation.eval_runner eval backspace-consistency

# Performance benchmarking
python -m src.evaluation.eval_runner benchmark --suite backspace-vibe --iterations 3
```

### Method 3: Custom Evaluation

```python
from src.evaluation.backspace_eval_suite import run_backspace_vibe_evaluation

# Run programmatically
results = await run_backspace_vibe_evaluation()
print(f"Success Rate: {results['success_rate']:.1%}")
```

## Understanding Results

### Success Criteria

**Minimum Viable Implementation:**
- ✅ API accessible and responds to requests
- ✅ Streaming events in correct SSE format
- ✅ At least 30% success rate on vibe evaluation
- ✅ PR creation functionality working

**Production Ready Implementation:**
- ✅ 70%+ success rate on vibe evaluation
- ✅ Average vibe score > 0.75
- ✅ Consistent performance patterns
- ✅ Response times < 2 minutes
- ✅ High-quality PR creation

### Focus Areas Identification

The evaluation will identify "focus areas" - tasks with inconsistent performance (50/50 success rate). These are your highest priority improvements:

```bash
⚠️  FOCUS AREAS (Inconsistent Performance):
  - user_authentication: slow_variable
  - async_database_fix: inconsistent
```

### Performance Patterns

- `fast_success`: Working well, good performance
- `slow_success`: Working but needs optimization
- `fast_failure`: Quick failure, likely implementation issue
- `slow_failure`: Timeout or complex failure
- `inconsistent`: **Most important** - needs immediate attention

## Troubleshooting

### Common Issues

1. **API Not Accessible**
   ```bash
   # Ensure server is running
   python -m src.api.main
   # Check http://localhost:8000/health
   ```

2. **Authentication Errors**
   ```bash
   # Check environment variables
   export ANTHROPIC_API_KEY=your_key
   export GITHUB_TOKEN=your_token
   ```

3. **Low Success Rates**
   - Check agent implementation logic
   - Verify sandbox isolation works
   - Test PR creation manually

4. **Inconsistent Performance**
   - Focus on the specific tasks identified
   - Check for environment-dependent issues
   - Validate error handling

### Debug Mode

```bash
# Enable detailed logging
export LOG_LEVEL=DEBUG
python test_backspace_evals.py
```

## Submission Checklist

Before submitting to Backspace:

- [ ] ✅ Integration test passes
- [ ] ✅ Vibe evaluation shows >50% success rate  
- [ ] ✅ PR creation works consistently
- [ ] ✅ SSE streaming format matches specification
- [ ] ✅ No critical security issues detected
- [ ] ✅ Performance within reasonable bounds (< 3 minutes per task)
- [ ] 📧 Results saved for submission documentation

## Advanced Usage

### Custom Evaluation Tasks

Create your own evaluation tasks:

```python
from src.evaluation.backspace_eval_suite import BackspaceEvalTask

custom_task = BackspaceEvalTask(
    task_id="my_custom_test",
    repo_url="https://github.com/my/repo",
    prompt="Add specific functionality",
    expected_changes=["app.py"],
    success_criteria={"feature_added": True},
    difficulty="medium",
    category="custom"
)
```

### Regression Detection

Compare performance between versions:

```bash
python -m src.evaluation.eval_runner compare \
  --baseline v1.0 \
  --current v1.1 \
  --suite backspace-vibe
```

### Continuous Benchmarking

Set up automated evaluation:

```python
from src.evaluation.backspace_test_runner import compare_with_baseline

# Run as part of CI/CD
baseline_result = await compare_with_baseline()
if baseline_result.get("performance_regression"):
    # Fail CI/CD pipeline
    exit(1)
```

## Resources

- **SWE-bench Paper**: Understanding the methodology behind these evaluations
- **Backspace Specification**: Original requirements document
- **Example Results**: Sample evaluation outputs in `examples/`
- **Debug Logs**: Detailed logging for troubleshooting

## Support

For evaluation issues:
1. Check this guide first
2. Review debug logs with `LOG_LEVEL=DEBUG`
3. Validate individual components work in isolation
4. Contact support with specific error messages and logs

Remember: The goal is building intuition about your system's performance, not just achieving high scores. Focus on understanding the patterns and improving consistently.