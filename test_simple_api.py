#!/usr/bin/env python3
"""
Simple test to verify the core API functionality
"""

import asyncio
import httpx
import json

async def test_simple_api():
    """Test the basic API functionality"""
    
    print("🧪 Testing Backspace API Core Functionality")
    print("=" * 50)
    
    api_url = "http://localhost:8000"
    
    # Test 1: Health check
    print("1. Testing health endpoint...")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{api_url}/health")
            if response.status_code == 200:
                print("   ✅ Health check passed")
                health_data = response.json()
                print(f"   📊 Components: {health_data.get('components', {})}")
            else:
                print(f"   ❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ Health check error: {e}")
            return False
    
    # Test 2: Simple streaming request
    print("\n2. Testing streaming API...")
    request_data = {
        "repo_url": "https://github.com/encode/starlette",  # Smaller repo
        "prompt": "Add a simple health check endpoint that returns status ok"
    }
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            async with client.stream(
                "POST", 
                f"{api_url}/code",
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    print(f"   ❌ API request failed: {response.status_code}")
                    return False
                
                print("   ✅ Streaming started")
                
                events = []
                event_types = set()
                
                async for chunk in response.aiter_text():
                    for line in chunk.split('\n'):
                        if line.strip().startswith('data: '):
                            try:
                                event_data = json.loads(line[6:])
                                events.append(event_data)
                                event_types.add(event_data.get('type', 'Unknown'))
                                
                                # Print key events
                                event_type = event_data.get('type', 'Unknown')
                                if event_type in ['Status', 'AI Message']:
                                    message = event_data.get('message', '')[:60]
                                    print(f"   📡 {event_type}: {message}...")
                                elif 'pr_url' in event_data:
                                    print(f"   🎉 PR Created: {event_data['pr_url']}")
                                    
                            except json.JSONDecodeError:
                                continue
                
                print(f"\n   📊 Total events: {len(events)}")
                print(f"   📋 Event types: {sorted(event_types)}")
                
                # Check for required events
                required_events = {'Status', 'AI Message', 'Tool: Read'}
                missing_events = required_events - event_types
                
                if missing_events:
                    print(f"   ⚠️  Missing events: {missing_events}")
                else:
                    print("   ✅ All required event types present")
                
                # Check for PR creation
                pr_created = any('pr_url' in event for event in events)
                if pr_created:
                    print("   ✅ PR creation attempted")
                else:
                    print("   ⚠️  No PR creation detected")
                
                return len(events) > 5 and not missing_events
                
    except Exception as e:
        print(f"   ❌ Streaming test error: {e}")
        return False

async def main():
    success = await test_simple_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 BASIC FUNCTIONALITY WORKING!")
        print("Your API is responding correctly to requests.")
        print("\nNext steps:")
        print("1. Check sandbox/repository access issues")
        print("2. Verify GitHub token permissions")
        print("3. Test with smaller repositories")
        print("4. Check E2B sandbox configuration")
    else:
        print("❌ BASIC FUNCTIONALITY ISSUES DETECTED")
        print("Please check:")
        print("1. API server is running (python -m src.api.main)")
        print("2. Environment variables are set correctly")
        print("3. Dependencies are installed")

if __name__ == "__main__":
    asyncio.run(main())
