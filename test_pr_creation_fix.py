#!/usr/bin/env python3
"""
Test script to verify PR creation fix
"""

import asyncio
import httpx
import json
import sys

async def test_pr_creation_fix():
    """Test the PR creation fix with a simple repository"""
    
    print("🔧 Testing PR Creation Fix")
    print("=" * 50)
    
    api_url = "http://localhost:8000"
    
    # Test with a small, simple repository
    request_data = {
        "repo_url": "https://github.com/encode/starlette",  # Well-structured repo
        "prompt": "Add a simple health check endpoint that returns {'status': 'healthy', 'timestamp': current_time}"
    }
    
    print(f"📡 Testing with repository: {request_data['repo_url']}")
    print(f"📝 Prompt: {request_data['prompt']}")
    print("\n🚀 Starting request...")
    
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:
            async with client.stream(
                "POST", 
                f"{api_url}/code",
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    print(f"❌ API request failed: {response.status_code}")
                    return False
                
                print("✅ Streaming started successfully")
                
                events = []
                key_events = {
                    'repo_cloned': False,
                    'files_found': False,
                    'branch_created': False,
                    'changes_made': False,
                    'branch_pushed': False,
                    'pr_created': False,
                    'pr_url': None
                }
                
                async for chunk in response.aiter_text():
                    for line in chunk.split('\n'):
                        if line.strip().startswith('data: '):
                            try:
                                event_data = json.loads(line[6:])
                                events.append(event_data)
                                
                                # Track key events
                                event_type = event_data.get('type', '')
                                message = event_data.get('message', '')
                                
                                if 'cloned successfully' in message:
                                    key_events['repo_cloned'] = True
                                    print(f"✅ Repository cloned: {message}")
                                
                                if 'Found' in message and 'files' in message:
                                    key_events['files_found'] = True
                                    print(f"✅ Files found: {message}")
                                
                                if event_type == 'Tool: Bash' and 'git checkout -b' in event_data.get('command', ''):
                                    key_events['branch_created'] = True
                                    print(f"✅ Branch created: {event_data.get('command', '')}")
                                
                                if event_type == 'Tool: Edit':
                                    key_events['changes_made'] = True
                                    filepath = event_data.get('filepath', '')
                                    print(f"✅ Code changes made: {filepath}")
                                
                                if event_type == 'Tool: Bash' and 'git push' in event_data.get('command', ''):
                                    key_events['branch_pushed'] = True
                                    print(f"✅ Branch pushed: {event_data.get('command', '')}")
                                
                                if 'pr_url' in event_data:
                                    key_events['pr_created'] = True
                                    key_events['pr_url'] = event_data['pr_url']
                                    print(f"🎉 PR CREATED: {event_data['pr_url']}")
                                
                                # Print status updates
                                if event_type == 'Status':
                                    print(f"📊 Status: {message}")
                                
                            except json.JSONDecodeError:
                                continue
                
                print(f"\n📊 Total events processed: {len(events)}")
                
                # Analyze results
                print("\n🔍 Analysis:")
                success_count = sum(1 for k, v in key_events.items() if v and k != 'pr_url')
                
                for key, status in key_events.items():
                    if key == 'pr_url':
                        continue
                    icon = "✅" if status else "❌"
                    print(f"  {icon} {key.replace('_', ' ').title()}: {status}")
                
                if key_events['pr_url']:
                    print(f"\n🎉 SUCCESS! PR URL: {key_events['pr_url']}")
                    return True
                elif success_count >= 4:  # Most steps completed
                    print(f"\n⚠️  PARTIAL SUCCESS: {success_count}/6 steps completed")
                    print("   The core functionality is working, PR creation may have failed")
                    return True
                else:
                    print(f"\n❌ FAILURE: Only {success_count}/6 steps completed")
                    return False
                
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

async def main():
    print("🧪 PR Creation Fix Test")
    print("This test verifies that the directory context fixes resolve the PR creation issue.")
    print()
    
    # Check if API is running
    try:
        async with httpx.AsyncClient() as client:
            health_response = await client.get("http://localhost:8000/health")
            if health_response.status_code != 200:
                print("❌ API server is not running or not healthy")
                print("Please start the server with: python -m src.api.main")
                return
    except Exception:
        print("❌ Cannot connect to API server")
        print("Please start the server with: python -m src.api.main")
        return
    
    success = await test_pr_creation_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 PR CREATION FIX SUCCESSFUL!")
        print("Your autonomous coding agent can now create PRs successfully.")
        print("\nNext steps:")
        print("1. Run the full evaluation suite: python test_backspace_evals.py")
        print("2. Test with different repositories")
        print("3. Submit to Backspace!")
    else:
        print("❌ PR CREATION STILL HAS ISSUES")
        print("Check the logs above for specific failure points.")
        print("\nDebugging tips:")
        print("1. Check GitHub token permissions")
        print("2. Verify E2B sandbox is working")
        print("3. Test with a simpler repository")

if __name__ == "__main__":
    asyncio.run(main())
