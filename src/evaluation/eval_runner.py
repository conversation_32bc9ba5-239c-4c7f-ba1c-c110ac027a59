"""
Command-line interface for running evaluations
"""

import asyncio
import argparse
import json
import logging
from pathlib import Path
from typing import Dict, Any

from .eval_framework import (
    AutonomousCodingAgentEvaluator,
    create_basic_eval_suite,
    create_swe_bench_style_suite
)
from .enhanced_evaluators import PassAtKEvaluator
from .tiered_datasets import get_suite_by_tier, get_all_tiered_suites
from .regression_detection import ContinuousBenchmarkManager
from .backspace_eval_suite import (
    create_backspace_vibe_eval_suite,
    create_backspace_consistency_suite,
    run_backspace_vibe_evaluation
)
from ..utils.config import get_settings
from dataclasses import asdict
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def run_evaluation(suite_name: str, output_file: str = None, k: int = 1) -> Dict[str, Any]:
    """Run an evaluation suite"""
    
    settings = get_settings()
    
    # Initialize evaluator
    evaluator = AutonomousCodingAgentEvaluator(
        anthropic_api_key=settings.anthropic_api_key,
        github_token=settings.github_token
    )
    
    # Get evaluation suite
    if suite_name == "basic":
        eval_suite = create_basic_eval_suite()
    elif suite_name == "swe-bench":
        eval_suite = create_swe_bench_style_suite()
    elif suite_name == "backspace-vibe":
        eval_suite = create_backspace_vibe_eval_suite()
    elif suite_name == "backspace-consistency":
        eval_suite = create_backspace_consistency_suite()
    elif suite_name in ["quick", "standard", "comprehensive"]:
        eval_suite = get_suite_by_tier(suite_name)
    else:
        raise ValueError(f"Unknown evaluation suite: {suite_name}. Available: basic, swe-bench, backspace-vibe, backspace-consistency, quick, standard, comprehensive")
    
    print(f"🚀 Running evaluation suite: {eval_suite.name}")
    print(f"📝 Description: {eval_suite.description}")
    print(f"📊 Total tasks: {len(eval_suite.tasks)}")
    print()
    
    # Run evaluation (with Pass@k if k > 1)
    if k > 1:
        pass_at_k_evaluator = PassAtKEvaluator(evaluator)
        print(f"Running Pass@{k} evaluation...")
        
        pass_at_k_results = []
        for task in eval_suite.tasks:
            pass_at_k_result = await pass_at_k_evaluator.evaluate_pass_at_k(task, k)
            pass_at_k_results.append(pass_at_k_result)
        
        # Create summary results
        total_tasks = len(pass_at_k_results)
        avg_success_rate = sum(r.success_rate for r in pass_at_k_results) / total_tasks
        avg_reliability = sum(r.reliability_score for r in pass_at_k_results) / total_tasks
        
        results = {
            "suite_name": eval_suite.name,
            "suite_version": eval_suite.version,
            "evaluation_type": f"Pass@{k}",
            "total_tasks": total_tasks,
            "average_success_rate": avg_success_rate,
            "average_reliability_score": avg_reliability,
            "pass_at_k_results": [asdict(r) for r in pass_at_k_results],
            "timestamp": datetime.now().isoformat()
        }
    else:
        results = await evaluator.evaluate_suite(eval_suite)
    
    # Print summary
    print("\n" + "="*60)
    print("📊 EVALUATION RESULTS")
    print("="*60)
    
    if k > 1:
        # Pass@k results structure
        print(f"Average Success Rate: {results['average_success_rate']:.1%}")
        print(f"Total Tasks: {results['total_tasks']}")
        print(f"Evaluation Type: {results['evaluation_type']}")
        print()
        print("Pass@k Metrics:")
        print(f"  Average Success Rate: {results['average_success_rate']:.2f}")
        print(f"  Average Reliability: {results['average_reliability_score']:.2f}")
    else:
        # Standard evaluation results structure
        print(f"Success Rate: {results['success_rate']:.1%} ({results['successful_tasks']}/{results['total_tasks']})")
        print(f"Average Execution Time: {results.get('average_execution_time', 0):.1f}s")
        print()
        print("Average Scores:")
        for metric, score in results.get('average_scores', {}).items():
            print(f"  {metric.title()}: {score:.2f}")
    print()
    
    # Print individual results
    print("Individual Task Results:")
    if k > 1:
        for result in results['pass_at_k_results']:
            print(f"  {result['task_id']}: {result['success_rate']:.1%} success rate, {result['reliability_score']:.2f} reliability")
    else:
        for result in results['individual_results']:
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            overall_score = (result['code_correctness'] + result['requirement_adherence'] + 
                           result['code_quality'] + result['safety_score'] + 
                           result['trajectory_score'] + result['context_efficiency']) / 6
            print(f"  {result['task_id']}: {status} (Overall: {overall_score:.2f})")
    
    # Save results if output file specified
    if output_file:
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_path}")
    
    return results


async def benchmark_performance(iterations: int = 3, suite_name: str = "basic"):
    """Run performance benchmarks"""
    print(f"🔥 Running performance benchmark ({iterations} iterations)")
    
    all_times = []
    all_scores = []
    
    for i in range(iterations):
        print(f"\n--- Iteration {i+1}/{iterations} ---")
        results = await run_evaluation(suite_name)
        
        all_times.append(results['average_execution_time'])
        overall_score = sum(results['average_scores'].values()) / len(results['average_scores'])
        all_scores.append(overall_score)
    
    avg_time = sum(all_times) / len(all_times)
    avg_score = sum(all_scores) / len(all_scores)
    
    print("\n" + "="*60)
    print("🏆 BENCHMARK RESULTS")
    print("="*60)
    print(f"Average Execution Time: {avg_time:.1f}s")
    print(f"Average Overall Score: {avg_score:.2f}")
    print(f"Performance Consistency: {(1 - (max(all_times) - min(all_times)) / avg_time):.1%}")
    
    return {
        "suite_name": suite_name,
        "iterations": iterations,
        "average_time": avg_time,
        "average_score": avg_score,
        "all_times": all_times,
        "all_scores": all_scores
    }


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(description="Autonomous Coding Agent Evaluator")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Eval command
    eval_parser = subparsers.add_parser('eval', help='Run evaluation suite')
    eval_parser.add_argument('suite', choices=['basic', 'swe-bench', 'backspace-vibe', 'backspace-consistency', 'quick', 'standard', 'comprehensive'], 
                           help='Evaluation suite to run')
    eval_parser.add_argument('--output', '-o', help='Output file for results (JSON)')
    eval_parser.add_argument('--pass-at-k', '-k', type=int, default=1, help='Run Pass@k evaluation')
    
    # Benchmark command
    benchmark_parser = subparsers.add_parser('benchmark', help='Run performance benchmark')
    benchmark_parser.add_argument('--iterations', '-i', type=int, default=3, help='Number of iterations')
    benchmark_parser.add_argument('--suite', '-s', default='basic', 
                                choices=['basic', 'swe-bench', 'backspace-vibe', 'backspace-consistency', 'quick', 'standard', 'comprehensive'],
                                help='Evaluation suite to benchmark')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available evaluation suites')
    
    # Compare command
    compare_parser = subparsers.add_parser('compare', help='Compare two versions for regressions')
    compare_parser.add_argument('--baseline', '-b', required=True, help='Baseline version')
    compare_parser.add_argument('--current', '-c', required=True, help='Current version')
    compare_parser.add_argument('--suite', '-s', required=True, help='Suite name')
    compare_parser.add_argument('--output', '-o', help='Output file for comparison report')
    
    args = parser.parse_args()
    
    if args.command == 'eval':
        asyncio.run(run_evaluation(args.suite, args.output, args.pass_at_k))
    elif args.command == 'benchmark':
        asyncio.run(benchmark_performance(args.iterations, args.suite))
    elif args.command == 'list':
        print("Available evaluation suites:")
        print("  basic               - Fundamental coding agent tests")
        print("  swe-bench           - Real-world software engineering tasks")
        print("  backspace-vibe      - Backspace vibe evaluation (5-10 tasks, SWE-bench methodology)")
        print("  backspace-consistency - Find 50/50 success rate scenarios")
        print("  quick               - Fast validation tests (5-10 tasks)")
        print("  standard            - Standard benchmark tests (50-100 tasks)")
        print("  comprehensive       - Production readiness tests (200+ tasks)")
    elif args.command == 'compare':
        from .regression_detection import RegressionDetector
        detector = RegressionDetector()
        try:
            comparison = detector.compare_versions(
                args.baseline, args.current, args.suite
            )
            report = detector.generate_report(comparison)
            print(report)
            
            if args.output:
                with open(args.output, 'w') as f:
                    f.write(report)
                print(f"\nReport saved to {args.output}")
        except Exception as e:
            print(f"Error comparing versions: {e}")
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
