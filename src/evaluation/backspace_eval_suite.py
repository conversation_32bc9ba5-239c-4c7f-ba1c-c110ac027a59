"""
Backspace-specific evaluation suite based on SWE-bench winning team insights
Focus on end-to-end workflow validation and vibe evals (5-10 examples)
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import httpx

from .eval_framework import EvalTask, <PERSON>lSuite, EvalResult
from ..utils.config import get_settings

logger = logging.getLogger(__name__)


@dataclass
class BackspaceEvalTask(EvalTask):
    """Extended evaluation task for Backspace specification"""
    streaming_required: bool = True
    pr_creation_required: bool = True
    sandbox_isolation_required: bool = True
    expected_sse_events: List[str] = None  # Expected SSE event types
    test_repository: str = None  # Specific test repo if different from repo_url
    
    def __post_init__(self):
        if self.expected_sse_events is None:
            # Default expected SSE events from Backspace spec
            self.expected_sse_events = [
                "Tool: Read",
                "AI Message", 
                "Tool: Edit",
                "Tool: Bash"
            ]


class BackspaceEndToEndEvaluator:
    """
    End-to-end evaluator for Backspace autonomous coding agent
    Tests the full API workflow including streaming, sandboxing, and PR creation
    """
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.settings = get_settings()
    
    async def evaluate_streaming_api(self, task: BackspaceEvalTask) -> Dict[str, Any]:
        """Test the streaming API endpoint matches Backspace specification"""
        
        metrics = {
            "api_accessible": False,
            "streaming_works": False,
            "sse_format_correct": False,
            "required_events_present": False,
            "pr_url_returned": False,
            "workflow_completed": False,
            "response_time": 0.0,
            "total_events": 0,
            "event_types_received": [],
            "pr_url": None,
            "error_message": None
        }
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Test the POST /code endpoint
            request_payload = {
                "repo_url": task.repo_url,
                "prompt": task.prompt,
                "branch_name": f"eval-test-{int(start_time)}"
            }
            
            async with httpx.AsyncClient(timeout=300.0) as client:
                # Test API accessibility
                try:
                    health_response = await client.get(f"{self.api_base_url}/health")
                    metrics["api_accessible"] = health_response.status_code == 200
                except:
                    metrics["error_message"] = "API not accessible"
                    return metrics
                
                # Test streaming endpoint
                async with client.stream(
                    "POST",
                    f"{self.api_base_url}/code",
                    json=request_payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status_code != 200:
                        metrics["error_message"] = f"API returned {response.status_code}"
                        return metrics
                    
                    metrics["streaming_works"] = True
                    event_count = 0
                    event_types = set()
                    
                    async for chunk in response.aiter_text():
                        for line in chunk.split('\n'):
                            if line.strip().startswith('data: '):
                                event_count += 1
                                try:
                                    # Parse SSE event
                                    event_data = json.loads(line[6:])  # Remove 'data: '
                                    event_type = event_data.get('type', 'Unknown')
                                    event_types.add(event_type)
                                    
                                    # Check for PR creation
                                    if 'pr_url' in event_data:
                                        metrics["pr_url"] = event_data['pr_url']
                                        metrics["pr_url_returned"] = True
                                    
                                    # Validate SSE format matches Backspace spec
                                    if event_type in ["Tool: Read", "Tool: Edit", "Tool: Bash", "AI Message"]:
                                        metrics["sse_format_correct"] = True
                                    
                                except json.JSONDecodeError:
                                    logger.warning(f"Invalid JSON in SSE event: {line}")
                    
                    metrics["total_events"] = event_count
                    metrics["event_types_received"] = list(event_types)
                    
                    # Check if required events are present
                    required_events = set(task.expected_sse_events)
                    received_events = set(event_types)
                    metrics["required_events_present"] = required_events.issubset(received_events)
                    
                    # Check workflow completion
                    metrics["workflow_completed"] = (
                        event_count > 5 and 
                        metrics["pr_url_returned"] and 
                        metrics["required_events_present"]
                    )
            
            metrics["response_time"] = asyncio.get_event_loop().time() - start_time
            
        except Exception as e:
            metrics["error_message"] = str(e)
            logger.error(f"Error in streaming API evaluation: {e}")
        
        return metrics
    
    async def evaluate_pr_quality(self, pr_url: str, task: BackspaceEvalTask) -> Dict[str, Any]:
        """Evaluate the quality of the created pull request"""
        
        if not pr_url or "github.com" not in pr_url:
            return {"pr_quality_score": 0.0, "error": "Invalid PR URL"}
        
        try:
            # Extract owner, repo, PR number from URL
            # https://github.com/owner/repo/pull/123
            parts = pr_url.split('/')
            if len(parts) < 7:
                return {"pr_quality_score": 0.0, "error": "Malformed PR URL"}
            
            owner = parts[3]
            repo = parts[4]
            pr_number = parts[6]
            
            # Use GitHub API to check PR details
            async with httpx.AsyncClient() as client:
                headers = {
                    "Authorization": f"token {self.settings.github_token}",
                    "Accept": "application/vnd.github.v3+json"
                }
                
                pr_response = await client.get(
                    f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}",
                    headers=headers
                )
                
                if pr_response.status_code != 200:
                    return {"pr_quality_score": 0.0, "error": "Cannot access PR"}
                
                pr_data = pr_response.json()
                
                # Get PR files
                files_response = await client.get(
                    f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}/files",
                    headers=headers
                )
                
                files_data = files_response.json() if files_response.status_code == 200 else []
                
                # Calculate PR quality score
                quality_metrics = {
                    "has_title": bool(pr_data.get("title", "").strip()),
                    "has_description": bool(pr_data.get("body", "").strip()),
                    "files_changed": len(files_data),
                    "appropriate_branch": pr_data.get("head", {}).get("ref", "") != "main",
                    "additions": pr_data.get("additions", 0),
                    "deletions": pr_data.get("deletions", 0)
                }
                
                # Score calculation
                score = 0.0
                if quality_metrics["has_title"]:
                    score += 0.2
                if quality_metrics["has_description"]:
                    score += 0.2
                if quality_metrics["files_changed"] > 0:
                    score += 0.3
                if quality_metrics["appropriate_branch"]:
                    score += 0.2
                if quality_metrics["additions"] > 0:
                    score += 0.1
                
                return {
                    "pr_quality_score": score,
                    "pr_details": quality_metrics,
                    "pr_title": pr_data.get("title", ""),
                    "pr_body": pr_data.get("body", ""),
                    "files_changed": [f.get("filename") for f in files_data]
                }
                
        except Exception as e:
            logger.error(f"Error evaluating PR quality: {e}")
            return {"pr_quality_score": 0.0, "error": str(e)}


class BackspaceVibeEvaluator:
    """
    Implements 'vibe eval' methodology from SWE-bench winning team
    5-10 carefully chosen examples to build intuition about system performance
    """
    
    def __init__(self):
        self.evaluator = BackspaceEndToEndEvaluator()
    
    async def evaluate_task_vibe(self, task: BackspaceEvalTask) -> Dict[str, Any]:
        """
        Evaluate a single task with vibe eval methodology
        Focus on end-to-end performance and edge case detection
        """
        
        logger.info(f"🔍 Vibe evaluating task: {task.task_id}")
        
        # Run end-to-end API evaluation
        api_metrics = await self.evaluator.evaluate_streaming_api(task)
        
        # Evaluate PR quality if PR was created
        pr_metrics = {}
        if api_metrics.get("pr_url"):
            pr_metrics = await self.evaluator.evaluate_pr_quality(
                api_metrics["pr_url"], task
            )
        
        # Calculate vibe score (holistic assessment)
        vibe_score = self._calculate_vibe_score(api_metrics, pr_metrics, task)
        
        # Classify performance pattern
        performance_pattern = self._classify_performance_pattern(api_metrics, task)
        
        return {
            "task_id": task.task_id,
            "vibe_score": vibe_score,
            "performance_pattern": performance_pattern,
            "api_metrics": api_metrics,
            "pr_metrics": pr_metrics,
            "success": vibe_score >= 0.7,
            "consistent": performance_pattern not in ["inconsistent", "slow_variable"],
            "timestamp": datetime.now().isoformat()
        }
    
    def _calculate_vibe_score(self, api_metrics: Dict, pr_metrics: Dict, task: BackspaceEvalTask) -> float:
        """Calculate holistic vibe score"""
        
        score = 0.0
        
        # API functionality (40%)
        if api_metrics.get("api_accessible"):
            score += 0.1
        if api_metrics.get("streaming_works"):
            score += 0.1
        if api_metrics.get("sse_format_correct"):
            score += 0.1
        if api_metrics.get("workflow_completed"):
            score += 0.1
        
        # PR creation and quality (40%)
        if api_metrics.get("pr_url_returned"):
            score += 0.2
        if pr_metrics.get("pr_quality_score", 0) > 0:
            score += 0.2 * pr_metrics["pr_quality_score"]
        
        # Performance and reliability (20%)
        response_time = api_metrics.get("response_time", float('inf'))
        if response_time < 60:  # Under 1 minute
            score += 0.1
        if api_metrics.get("total_events", 0) > 5:  # Sufficient activity
            score += 0.1
        
        return min(1.0, score)
    
    def _classify_performance_pattern(self, api_metrics: Dict, task: BackspaceEvalTask) -> str:
        """
        Classify performance pattern to identify inconsistent cases
        Key insight from SWE-bench: focus on 50/50 success rate scenarios
        """
        
        response_time = api_metrics.get("response_time", 0)
        success = api_metrics.get("workflow_completed", False)
        
        if success and response_time < 30:
            return "fast_success"
        elif success and response_time > 60:
            return "slow_success"
        elif not success and response_time > 60:
            return "slow_failure"
        elif not success and response_time < 30:
            return "fast_failure"
        else:
            return "inconsistent"  # This is what we want to focus on!


def create_backspace_vibe_eval_suite() -> EvalSuite:
    """
    Create vibe eval suite with 5-10 carefully chosen examples
    Based on SWE-bench insights: start with vibe evals to build intuition
    """
    
    tasks = [
        # Easy - Health check (should always work)
        BackspaceEvalTask(
            task_id="health_endpoint_simple",
            repo_url="https://github.com/tiangolo/fastapi",  # Well-structured repo
            prompt="Add a health check endpoint that returns status and version",
            expected_changes=["main.py", "app.py"],
            success_criteria={"endpoint_added": True},
            difficulty="easy",
            category="api",
            expected_sse_events=["Tool: Read", "AI Message", "Tool: Edit", "Tool: Bash"]
        ),
        
        # Easy-Medium - Input validation (common request)
        BackspaceEvalTask(
            task_id="input_validation_posts",
            repo_url="https://github.com/encode/django-rest-framework",
            prompt="Add input validation to all POST endpoints using appropriate validation",
            expected_changes=["serializers.py", "views.py"],
            success_criteria={"validation_added": True},
            difficulty="medium", 
            category="security"
        ),
        
        # Medium - Real feature addition
        BackspaceEvalTask(
            task_id="user_authentication",
            repo_url="https://github.com/pallets/flask",
            prompt="Add basic user authentication with login and logout endpoints",
            expected_changes=["app.py", "auth.py"],
            success_criteria={"auth_implemented": True},
            difficulty="medium",
            category="feature"
        ),
        
        # Medium-Hard - Bug fix (tests existing logic)
        BackspaceEvalTask(
            task_id="async_database_fix",
            repo_url="https://github.com/sqlalchemy/sqlalchemy",
            prompt="Fix the async session management issue in the connection pool",
            expected_changes=["async_connection.py", "pool.py"], 
            success_criteria={"bug_fixed": True},
            difficulty="hard",
            category="bug_fix"
        ),
        
        # Hard - Complex refactoring (edge case detection)
        BackspaceEvalTask(
            task_id="microservice_split",
            repo_url="https://github.com/fastapi/fastapi",
            prompt="Split the user management functionality into a separate microservice",
            expected_changes=["user_service.py", "main.py", "docker-compose.yml"],
            success_criteria={"microservice_created": True},
            difficulty="hard",
            category="refactor"
        ),
        
        # Consistency test - Same type, different complexity
        BackspaceEvalTask(
            task_id="logging_simple",
            repo_url="https://github.com/python/cpython",
            prompt="Add basic logging to the main application entry point",
            expected_changes=["main.py"],
            success_criteria={"logging_added": True},
            difficulty="medium",
            category="feature"
        ),
        
        BackspaceEvalTask(
            task_id="logging_complex",
            repo_url="https://github.com/python/cpython", 
            prompt="Implement structured logging with correlation IDs and distributed tracing",
            expected_changes=["logging_config.py", "middleware.py", "main.py"],
            success_criteria={"structured_logging": True},
            difficulty="hard",
            category="feature"
        ),
        
        # Edge case - Minimal repo (tests robustness)
        BackspaceEvalTask(
            task_id="minimal_repo_enhancement",
            repo_url="https://github.com/python/hello-world",  # Very simple repo
            prompt="Add error handling and input validation to the main function",
            expected_changes=["hello.py"],
            success_criteria={"error_handling_added": True},
            difficulty="easy",
            category="enhancement"
        )
    ]
    
    return EvalSuite(
        name="Backspace Vibe Evaluation Suite",
        description="5-10 carefully chosen examples following SWE-bench vibe eval methodology",
        tasks=tasks,
        version="1.0",
        tier="vibe"
    )


def create_backspace_consistency_suite() -> EvalSuite:
    """
    Create suite specifically designed to find 50/50 success rate scenarios
    Key insight: These reveal the most about system limitations
    """
    
    tasks = [
        # Same prompt, different repos (consistency test)
        BackspaceEvalTask(
            task_id="validation_consistency_1",
            repo_url="https://github.com/fastapi/fastapi",
            prompt="Add input validation to POST endpoints",
            expected_changes=["main.py"],
            success_criteria={"validation_added": True},
            difficulty="medium",
            category="consistency_test"
        ),
        
        BackspaceEvalTask(
            task_id="validation_consistency_2", 
            repo_url="https://github.com/pallets/flask",
            prompt="Add input validation to POST endpoints",
            expected_changes=["app.py"],
            success_criteria={"validation_added": True},
            difficulty="medium",
            category="consistency_test"
        ),
        
        # Boundary conditions (likely to be inconsistent)
        BackspaceEvalTask(
            task_id="large_codebase",
            repo_url="https://github.com/django/django",
            prompt="Add rate limiting middleware",
            expected_changes=["middleware.py"],
            success_criteria={"rate_limiting_added": True},
            difficulty="hard",
            category="boundary_test"
        ),
        
        # Ambiguous requirements (tests agent reasoning)
        BackspaceEvalTask(
            task_id="improve_performance",
            repo_url="https://github.com/encode/uvicorn",
            prompt="Improve the application performance",
            expected_changes=["server.py"],
            success_criteria={"performance_improved": True},
            difficulty="hard",
            category="ambiguous"
        )
    ]
    
    return EvalSuite(
        name="Backspace Consistency Analysis Suite",
        description="Find 50/50 success rate scenarios that reveal system limitations",
        tasks=tasks,
        version="1.0", 
        tier="consistency"
    )


async def run_backspace_vibe_evaluation() -> Dict[str, Any]:
    """
    Run the complete Backspace vibe evaluation
    Entry point for SWE-bench style evaluation
    """
    
    print("🎯 Running Backspace Vibe Evaluation")
    print("Following SWE-bench winning team methodology")
    print()
    
    # Create evaluator
    vibe_evaluator = BackspaceVibeEvaluator()
    
    # Get vibe eval suite
    vibe_suite = create_backspace_vibe_eval_suite()
    
    print(f"📋 Evaluating {len(vibe_suite.tasks)} vibe tasks...")
    
    # Run evaluations
    results = []
    for task in vibe_suite.tasks:
        print(f"  🔍 {task.task_id} ({task.difficulty})...")
        
        result = await vibe_evaluator.evaluate_task_vibe(task)
        results.append(result)
        
        # Immediate feedback (vibe eval style)
        status = "✅" if result["success"] else "❌" 
        pattern = result["performance_pattern"]
        score = result["vibe_score"]
        
        print(f"    {status} Score: {score:.2f}, Pattern: {pattern}")
    
    # Analyze results
    success_rate = sum(1 for r in results if r["success"]) / len(results)
    avg_score = sum(r["vibe_score"] for r in results) / len(results)
    
    # Find inconsistent patterns (key insight!)
    inconsistent_tasks = [r for r in results if not r["consistent"]]
    
    print()
    print("📊 VIBE EVALUATION RESULTS")
    print("="*50)
    print(f"Success Rate: {success_rate:.1%}")
    print(f"Average Vibe Score: {avg_score:.2f}")
    print(f"Inconsistent Tasks: {len(inconsistent_tasks)}")
    
    if inconsistent_tasks:
        print()
        print("⚠️  FOCUS AREAS (Inconsistent Performance):")
        for task in inconsistent_tasks:
            print(f"  - {task['task_id']}: {task['performance_pattern']}")
    
    return {
        "suite_name": vibe_suite.name,
        "total_tasks": len(results),
        "success_rate": success_rate,
        "average_vibe_score": avg_score,
        "inconsistent_tasks": len(inconsistent_tasks),
        "results": results,
        "focus_areas": [t["task_id"] for t in inconsistent_tasks],
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    # Quick test
    asyncio.run(run_backspace_vibe_evaluation())