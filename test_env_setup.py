#!/usr/bin/env python3
"""
Quick test to verify environment setup
Run this after setting up your .env file
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_environment_setup():
    """Test that all required environment variables are set"""
    
    print("🔍 ENVIRONMENT SETUP TEST")
    print("=" * 40)
    
    # Load environment variables
    from src.utils.config import get_settings
    
    try:
        settings = get_settings()
        
        # Test Anthropic API key
        if settings.anthropic_api_key and settings.anthropic_api_key != "your_anthropic_api_key_here":
            print("✅ ANTHROPIC_API_KEY: Set correctly")
            anthropic_ok = True
        else:
            print("❌ ANTHROPIC_API_KEY: Missing or not set")
            print("   Get it from: https://console.anthropic.com/")
            anthropic_ok = False
        
        # Test GitHub token
        if settings.github_token and settings.github_token != "your_github_token_here":
            print("✅ GITHUB_TOKEN: Set correctly")
            github_ok = True
        else:
            print("❌ GITHUB_TOKEN: Missing or not set")
            print("   Get it from: https://github.com/settings/tokens")
            github_ok = False
        
        # Test E2B (optional)
        if settings.e2b_api_key and settings.e2b_api_key != "your_e2b_api_key_here":
            print("✅ E2B_API_KEY: Set (optional)")
        else:
            print("⚠️  E2B_API_KEY: Not set (will use local sandbox)")
        
        # Overall status
        if anthropic_ok and github_ok:
            print("\n🎉 ENVIRONMENT SETUP COMPLETE!")
            print("You can now run: python test_backspace_evals.py")
            return True
        else:
            print("\n❌ ENVIRONMENT SETUP INCOMPLETE")
            print("Please set the missing API keys in .env file")
            return False
            
    except Exception as e:
        print(f"❌ Error loading settings: {e}")
        return False

def test_api_connections():
    """Test that API connections work"""
    
    print("\n🔌 API CONNECTION TEST")
    print("=" * 40)
    
    from src.utils.config import get_settings
    settings = get_settings()
    
    # Test Anthropic
    try:
        import anthropic
        client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
        
        # Quick test call
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=10,
            messages=[{"role": "user", "content": "Hello"}]
        )
        print("✅ Anthropic API: Connected successfully")
        anthropic_ok = True
    except Exception as e:
        print(f"❌ Anthropic API: Failed - {e}")
        anthropic_ok = False
    
    # Test GitHub
    try:
        from github import Github
        g = Github(settings.github_token)
        user = g.get_user()
        print(f"✅ GitHub API: Connected as {user.login}")
        github_ok = True
    except Exception as e:
        print(f"❌ GitHub API: Failed - {e}")
        github_ok = False
    
    return anthropic_ok and github_ok

if __name__ == "__main__":
    # Test environment setup
    env_ok = test_environment_setup()
    
    if env_ok:
        # Test API connections
        api_ok = test_api_connections()
        
        if api_ok:
            print("\n🚀 ALL TESTS PASSED!")
            print("Your system is ready for Backspace evaluation!")
        else:
            print("\n⚠️  Environment set but API connections failed")
            print("Check your API keys are valid")
    else:
        print("\n📋 NEXT STEPS:")
        print("1. Edit .env file with your real API keys")
        print("2. Run this test again: python test_env_setup.py")
        print("3. Then run: python test_backspace_evals.py")